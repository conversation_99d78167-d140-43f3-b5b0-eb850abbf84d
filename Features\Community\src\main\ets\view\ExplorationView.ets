// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入通用模块中的全局信息模型和页面上下文类型
import type { GlobalInfoModel, PageContext } from '@ohos/common';
// 导入通用模块中的通用常量
import { CommonConstants } from '@ohos/common';
// 导入通用业务模块中的相关组件和类型
import {
  BaseHomeView,
  FullScreenNavigation,
  FullScreenNavigationData,
} from '@ohos/commonbusiness';
// 导入通用模块中的加载模型
import { LoadingModel } from '@ohos/common';

// 使用Component装饰器定义探索视图组件，启用非活动时冻结优化
@Component({ freezeWhenInactive: true })
export struct ExplorationView {
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用StorageProp装饰器获取系统颜色模式
  @StorageProp('systemColorMode') systemColorMode: ConfigurationConstant.ColorMode =
    AppStorage.get('systemColorMode')!;
  // 使用State装饰器定义导航状态高度
  @State naviStatusHeight: number = CommonConstants.NAVIGATION_HEIGHT + this.globalInfoModel.statusBarHeight;
  // 定义私有探索页面上下文
  private explorationPageContext: PageContext = AppStorage.get('explorationPageContext')!;

  // 定义即将出现的生命周期方法
  aboutToAppear(): void {
    // 初始化页面状态
  }

  // 使用Builder装饰器定义空白内容构建器
  @Builder
  EmptyContentBuilder() {
    // 创建空白区域
    Column() {
      // 空白内容
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.background_secondary'))
  }

  // 使用Builder装饰器定义内容视图构建器
  @Builder
  ContentViewBuilder() {
    // 调用空白内容构建器
    this.EmptyContentBuilder()
  }

  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建全屏导航
    FullScreenNavigation({
      topNavigationData: (() => {
        const navData = new FullScreenNavigationData();
        navData.title = '社区';
        return navData;
      })(),
    })
  }

  // 定义构建方法
  build() {
    // 创建导航组件
    Navigation(this.explorationPageContext.navPathStack) {
      // 创建基础首页视图
      BaseHomeView({
        loadingModel: new LoadingModel(),
        contentView: () => {
          // 调用内容视图构建器
          this.ContentViewBuilder()
        },
        topTitleView: () => {
          // 调用顶部标题视图构建器
          this.TopTitleViewBuilder()
        },
        reloadData: () => {
          // 重新加载数据的空实现
        },
      })
    }
    // 设置导航模式
    .mode(NavigationMode.Stack)
    // 隐藏标题栏
    .hideTitleBar(true)
  }
}