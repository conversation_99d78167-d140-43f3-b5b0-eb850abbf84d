// 导入基础服务工具包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的基础状态、基础视图模型、日志工具、页面上下文、偏好设置管理器
import { BaseState, BaseVM, Logger, PageContext, PreferenceManager } from '@ohos/common';
// 导入首页模块中的首页列表模型
import { HomeListModel } from '@ohos/home';

// 日志标签常量
const TAG: string = '[SplashViewModel]';

/**
 * 启动页视图模型类
 * 继承自BaseVM，负责处理启动页的业务逻辑
 * 包括首次启动检查、资源预加载、页面跳转等功能
 */
export class SplashViewModel extends BaseVM<BaseState> {
  // 页面上下文，用于页面导航
  private pageContext: PageContext = AppStorage.get('pageContext') as PageContext;
  // 首页列表模型单例，管理首页数据
  private homeListModel: HomeListModel = HomeListModel.getInstance();
  // 偏好设置管理器单例，管理应用配置
  private preferenceManager: PreferenceManager = PreferenceManager.getInstance();

  /**
   * 构造函数
   * 初始化基础状态
   */
  public constructor() {
    super(new BaseState());
  }

  /**
   * 发送事件方法
   * 根据事件类型执行相应的操作
   * @param eventType 启动页事件类型枚举
   */
  public sendEvent(eventType: SplashEventTypeEnum): void {
    // 根据事件类型执行相应操作
    if (eventType === SplashEventTypeEnum.JUMP_TO_MAIN) {
      // 跳转到主页面
      this.jumpToMainPage();
    } else if (eventType === SplashEventTypeEnum.PRELOAD_RESOURCES) {
      // 预加载资源
      this.preloadResources();
    } else if (eventType === SplashEventTypeEnum.CHECK_FIRST_START) {
      // 检查是否首次启动
      this.checkIsFirstStart();
    }
  }

  /**
   * 跳转到主页面的私有方法
   * 使用页面上下文替换当前页面为主页面
   */
  private jumpToMainPage(): void {
    // 替换当前页面为主页面
    this.pageContext.replacePage({
      routerName: 'MainPage',
    });
  }

  /**
   * 预加载资源的私有方法
   * 预加载各个模块的数据以提升用户体验
   */
  private preloadResources(): void {
    // 预加载首页列表数据
    this.homeListModel.preloadHomeData();
  }

  /**
   * 检查是否首次启动的私有方法
   * 通过偏好设置检查应用是否首次启动
   */
  private checkIsFirstStart(): void {
    // 检查偏好设置中是否存在首次启动标记
    this.preferenceManager.hasValue('isFirstStart').then((hasResult: boolean) => {
      if (hasResult) {
        // 不是首次启动
        Logger.info(TAG, 'Not first startup.');
      } else {
        // 首次启动
        Logger.info(TAG, 'First startup.');
        // 设置首次启动标记为false
        this.preferenceManager.setValue('isFirstStart', false).then(() => {
          Logger.info(TAG, 'Put the value of startup Successfully.');
        }).catch((err: BusinessError) => {
          Logger.error(TAG, `Put the value of startup Failed, err code: ${err.code}, message: ${err.message}`);
        });
      }
    }).catch((err: BusinessError) => {
      Logger.error(TAG, `check startup Failed, err code: ${err.code}, message: ${err.message}`);
    });
  }
}

/**
 * 启动页事件类型枚举
 * 定义启动页中可能发生的各种事件类型
 */
export enum SplashEventTypeEnum {
  // 跳转到主页面事件
  JUMP_TO_MAIN = 'jumpToMainPage',
  // 预加载资源事件
  PRELOAD_RESOURCES = 'preloadResources',
  // 检查首次启动事件
  CHECK_FIRST_START = 'checkIsFirstStart',
}